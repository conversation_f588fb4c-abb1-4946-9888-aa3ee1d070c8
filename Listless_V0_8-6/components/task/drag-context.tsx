"use client"

import { createContext, useContext, useState, useEffect, useMemo, type ReactNode } from "react"
import dynamic from "next/dynamic"
import {
  DndContext,
  type DragEndEvent,
  type DragOverEvent,
  type DragStartEvent,
  type DragCancelEvent,
  PointerSensor,
  KeyboardSensor,
  useSensor,
  useSensors,
  useDndMonitor,
  MeasuringStrategy,
  defaultDropAnimationSideEffects,
  closestCenter,
  type UniqueIdentifier,
} from "@dnd-kit/core"
import {
  sortableKeyboardCoordinates,
  arrayMove,
} from "@dnd-kit/sortable"
import { useTaskContext } from "./task-context"
import { useTaskMovement, useBulkTaskMovement } from "@/hooks/use-task-movement"
import { useReorderTasks } from "@/hooks/use-tasks"

type DragContextType = {
  activeTaskId: string | null
  selectedTaskIds: Set<string>
  isDraggingTask: boolean
  activeDroppableId: string | null
  isDraggingOver: (id: string) => boolean
  setSelectedTaskIds: (ids: Set<string>) => void
  isMultiDragging: boolean
  dragType: 'sorting' | 'cross-list' | null
  sortableItems: UniqueIdentifier[]
  setSortableItems: (items: UniqueIdentifier[]) => void
}

const DragContext = createContext<DragContextType>({
  activeTaskId: null,
  selectedTaskIds: new Set<string>(),
  isDraggingTask: false,
  activeDroppableId: null,
  isDraggingOver: () => false,
  setSelectedTaskIds: () => {},
  isMultiDragging: false,
  dragType: null,
  sortableItems: [],
  setSortableItems: () => {},
})

export function useDragContext() {
  return useContext(DragContext)
}

// Monitor component to catch all drag events
function DragMonitor() {
  // Keep monitor lightweight; avoid per-frame work/logging
  useDndMonitor({
    onDragStart(event) {
      // console.debug("MONITOR: onDragStart", { id: event.active?.id })
    },
    // Intentionally omit onDragMove to reduce main-thread pressure
    onDragOver(event) {
      // console.debug("MONITOR: onDragOver", { over: event.over?.id })
    },
    onDragEnd(event) {
      // console.debug("MONITOR: onDragEnd", { id: event.active?.id, over: event.over?.id })
    },
    onDragCancel() {
      // console.debug("MONITOR: onDragCancel")
    },
  })
  return null
}

export function DragContextProvider({ children }: { children: ReactNode }) {
  const { moveTasksToList, findTaskList } = useTaskContext()
  const taskMovement = useTaskMovement()
  const bulkTaskMovement = useBulkTaskMovement()
  const reorderTasksMutation = useReorderTasks()
  const [activeTaskId, setActiveTaskId] = useState<string | null>(null)
  const [selectedTaskIds, setSelectedTaskIdsInternal] = useState<Set<string>>(new Set())
  const [activeDroppableId, setActiveDroppableId] = useState<string | null>(null)
  // Removed droppableAreas to avoid frequent object churn; rely on activeDroppableId
  const [isMultiDragging, setIsMultiDragging] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [dragType, setDragType] = useState<'sorting' | 'cross-list' | null>(null)
  const [sortableItems, setSortableItems] = useState<UniqueIdentifier[]>([])

  const setSelectedTaskIds = (ids: Set<string>) => {
    setSelectedTaskIdsInternal(ids)
  }

  // Default sensors without custom activation constraints
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // Add some debugging to the sensor
      onActivation: (event) => {
        console.log("🎯 SENSOR: PointerSensor activated", event)
      }
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  // Set client-side flag to prevent hydration issues
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Note: sortableItems will now be managed by the TaskList component
  // This removes the dependency on the old task context system

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    console.log("🚀 DRAG START EVENT FIRED:", {
      activeId: active.id,
      activeData: active.data.current,
      timestamp: Date.now()
    })

    // Check if we're dragging a task by validating the drag data type or checking if it's in sortableItems
    const dragData = active.data.current
    const isTaskDrag = dragData?.type === "sortable-task" || sortableItems.includes(active.id)

    if (typeof active.id === "string" && isTaskDrag) {
      console.log("✅ Valid task drag detected, setting state...")
      setActiveTaskId(active.id)

      // Determine drag type based on the active element's data
      const isSortingDrag = dragData?.type === "sortable-task"
      setDragType(isSortingDrag ? 'sorting' : 'cross-list')

      // Check if we're dragging a selected task and there are multiple selections
      const isSelectedTask = selectedTaskIds.has(active.id)
      const isMultipleSelection = selectedTaskIds.size > 1

      setIsMultiDragging(isSelectedTask && isMultipleSelection)

      console.log("📊 Drag state set:", {
        taskId: active.id,
        dragType: isSortingDrag ? 'sorting' : 'cross-list',
        isSelected: isSelectedTask,
        multipleSelected: isMultipleSelection,
        isMultiDragging: isSelectedTask && isMultipleSelection,
        selectedTasks: Array.from(selectedTaskIds),
      })
    } else {
      console.log("❌ Invalid drag target:", active.id, "- not a task or not in sortable items")
    }
  }

  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event

    // Only update when value actually changes to avoid re-renders each frame
    if (over && typeof over.id === "string" && over.id.startsWith("sidebar-")) {
      const targetId = over.id.replace("sidebar-", "")

      // Update active droppable only when it changes
      setActiveDroppableId(prev => (prev !== targetId ? targetId : prev))

      // No droppableAreas map; visual feedback should use isOver from useDroppable locally
    } else {
      // Clear only if previously set
      setActiveDroppableId(prev => (prev !== null ? null : prev))
    }
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    console.log("🏁 DRAG END EVENT FIRED:", {
      activeId: active?.id,
      overId: over?.id,
      activeTaskId,
      isMultiDragging,
      selectedTasks: Array.from(selectedTaskIds),
      dragType,
      sortableItems: sortableItems.length,
      timestamp: Date.now()
    })

    // Store current state before resetting
    const currentActiveTaskId = activeTaskId
    const currentDragType = dragType
    const currentIsMultiDragging = isMultiDragging

    // Reset state
    console.log("🔄 Resetting drag state...")
    setActiveTaskId(null)
    setActiveDroppableId(null)
    // droppableAreas removed
    setIsMultiDragging(false)
    setDragType(null)

    // Handle sorting within the same list
    console.log("🔍 CHECKING SORTING CONDITIONS:", {
      currentDragType,
      currentActiveTaskId,
      overId: over?.id,
      overIdType: typeof over?.id,
      overIdInSortableItems: over?.id ? sortableItems.includes(over.id) : false,
      sortableItemsCount: sortableItems.length,
      sortableItems: sortableItems.slice(0, 5) // Show first 5 for debugging
    })

    if (currentDragType === 'sorting' && currentActiveTaskId && over && typeof over.id === "string" && sortableItems.includes(over.id)) {
      const activeIndex = sortableItems.indexOf(currentActiveTaskId)
      const overIndex = sortableItems.indexOf(over.id)

      if (activeIndex !== overIndex && activeIndex !== -1 && overIndex !== -1) {
        const newItems = arrayMove(sortableItems, activeIndex, overIndex)
        setSortableItems(newItems)

        console.log("🔄 Reordering tasks:", { from: activeIndex, to: overIndex, newOrder: newItems })

        // Create moves array for the reorder API
        const moves = newItems.map((taskId, index) => ({
          id: taskId as string,
          sort_order: index
        }))

        // Call the reorder API
        reorderTasksMutation.mutate({ moves }, {
          onSuccess: () => {
            console.log("✅ Task reordering successful")
          },
          onError: (error) => {
            console.error("❌ Task reordering failed:", error)
            // Revert the optimistic update on error
            setSortableItems(sortableItems)
          }
        })
      }
    }
    // If we have an active task and it's dropped on a sidebar item (cross-list drag)
    if (currentActiveTaskId && over && typeof over.id === "string" && over.id.startsWith("sidebar-")) {
      const targetView = over.id.replace("sidebar-", "")
      const sourceListId = findTaskList(currentActiveTaskId)

      console.log("Moving task(s) to:", targetView, "from:", sourceListId)

      // Determine if this is a project drop
      const isProjectDrop = !['inbox', 'today', 'scheduled', 'deferred', 'completed', 'trash'].includes(targetView)
      const projectId = isProjectDrop ? targetView : undefined

      if (currentIsMultiDragging && selectedTaskIds.size > 1) {
        // Move all selected tasks using bulk movement
        const tasksToMove = Array.from(selectedTaskIds).map(taskId => ({
          taskId,
          targetView: isProjectDrop ? 'project' : targetView,
          projectId,
        }))

        console.log("Moving multiple tasks:", tasksToMove)
        try {
          bulkTaskMovement.mutate(tasksToMove, {
            onError: (error) => {
              console.error('❌ Bulk task movement failed:', error)
              // Don't let the error freeze the UI
            },
            onSuccess: () => {
              console.log('✅ Bulk task movement successful')
            }
          })
        } catch (error) {
          console.error('❌ Error initiating bulk task movement:', error)
        }
      } else {
        // Move single task
        console.log("Moving single task:", currentActiveTaskId, "to:", targetView)
        try {
          taskMovement.mutate({
            taskId: currentActiveTaskId,
            targetView: isProjectDrop ? 'project' : targetView,
            projectId,
          }, {
            onError: (error) => {
              console.error('❌ Task movement failed:', error)
              // Don't let the error freeze the UI
            },
            onSuccess: () => {
              console.log('✅ Task movement successful')
            }
          })
        } catch (error) {
          console.error('❌ Error initiating task movement:', error)
        }
      }

      // Clear selection after successful drop
      setSelectedTaskIdsInternal(new Set())
    }
  }

  // Helper function to check if dragging over a specific area
  const isDraggingOver = (id: string): boolean => {
    const fullId = id.startsWith("sidebar-") ? id : `sidebar-${id}`
    // Consider "over" when activeDroppableId matches the target id
    return activeDroppableId === fullId.replace("sidebar-", "")
  }

  // Custom drop animation to maintain consistent size
  const dropAnimation = {
    sideEffects: defaultDropAnimationSideEffects({
      styles: {
        active: {
          opacity: "0.5",
        },
      },
    }),
  }

  // Always call hooks in the same order. Avoid early returns that skip hooks.
  const contextValue = useMemo(() => ({
    activeTaskId,
    selectedTaskIds,
    isDraggingTask: activeTaskId !== null,
    activeDroppableId,
    isDraggingOver,
    setSelectedTaskIds,
    isMultiDragging,
    dragType,
    sortableItems,
    setSortableItems,
  }), [activeTaskId, selectedTaskIds, activeDroppableId, isMultiDragging, dragType, sortableItems])

  return (
    <DragContext.Provider value={contextValue}>
      {isClient ? (
        <DndContext
          sensors={sensors}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
          collisionDetection={closestCenter}
          measuring={{
            droppable: {
              // Measure only during active drag to reduce overhead
              strategy: MeasuringStrategy.WhileDragging,
            },
          }}
          modifiers={[]}
          // @ts-ignore - Type issue with dropAnimation
          dropAnimation={dropAnimation}
        >
          <DragMonitor />
          {children}
        </DndContext>
      ) : (
        children
      )}
    </DragContext.Provider>
  )
}
